{"summarization": {"total_threats": 1, "unique_threat_types": 1, "classification_accuracy": 1.0, "classification_stats": {"DDoS": {"count": 1, "correct": 1}}, "threat_summaries": [{"id": "threat_0001", "classified_type": "DDoS", "confidence": 0.3333333333333333, "original_type": "DDoS", "classification_correct": true, "description_snippet": "Test DDoS attack...", "matched_keywords": ["ddos"]}], "processing_time": "2025-07-25T10:47:02.287213"}, "prompt": {"input_file": "test_workflow_data.json", "processing_time": "2025-07-25T10:47:02.288564", "total_records": 1, "processed_successfully": 1, "failed_records": 0, "threat_type_distribution": {"DDoS": 1}, "processed_data": [{"description": "Layer: [ground]\\nNetwork Type: [Network Traffic]\\nThreat Type: [DDoS]\\nTimestamp: [2024-04-13]\\nDetails: Test DDoS attack", "threat_type": "DDoS", "timestamp": "2024-04-13", "layer": "ground", "network_type": "Network Traffic", "index": 0, "original_data": [false, "ground", "Network Traffic", "DDoS", "2024-04-13", "Test DDoS attack", "Tag", 0]}]}, "specific_advice": [{"Description": "Layer: [ground]\\nNetwork Type: [Network Traffic]\\nThreat Type: [DDoS]\\nTimestamp: [2024-04-13]\\nDetails: Test DDoS attack", "Base Score": 7.5, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"], "Threat Type": "DDoS", "Advice": "Implement DDoS protection measures, rate limiting, and traffic filtering. High priority - immediate action required.", "Processing Time": "2025-07-25T10:47:02.289078", "original_index": 0}], "comprehensive_decision": [{"input_threat_analysis": {"Description": "Layer: [ground]\\nNetwork Type: [Network Traffic]\\nThreat Type: [DDoS]\\nTimestamp: [2024-04-13]\\nDetails: Test DDoS attack", "Base Score": 7.5, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"], "Threat Type": "DDoS", "Advice": "Implement DDoS protection measures, rate limiting, and traffic filtering. High priority - immediate action required.", "Processing Time": "2025-07-25T10:47:02.289078", "original_index": 0}, "selected_strategies": ["Implement DDoS protection and rate limiting", "Deploy traffic filtering and monitoring", "Establish incident response procedures", "Configure network segmentation"], "strategy_selection_confidence": 0.8, "threat_priority": "HIGH", "strategy_count": 4, "processing_time": "2025-07-25T10:47:02.289566"}], "workflow_summary": {"workflow_completion_time": "2025-07-25T10:47:02.289805", "total_duration_seconds": 0.002971, "agents_executed": ["summarization", "prompt", "specific_advice", "comprehensive_decision"], "data_flow_validation": {"summarization_output_valid": true, "prompt_output_valid": true, "specific_advice_output_valid": true, "comprehensive_decision_output_valid": true, "data_flow_continuous": true}, "performance_metrics": {"summarization_threats_processed": 1, "summarization_accuracy": 1.0, "prompt_records_processed": 1, "prompt_success_rate": 1.0, "specific_advice_samples_processed": 1, "average_cvss_score": 7.5, "comprehensive_decisions_made": 1, "total_strategies_selected": 4, "average_strategies_per_threat": 4.0}}}