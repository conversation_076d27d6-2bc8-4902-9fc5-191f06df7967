{"overall_summary": "威胁分析总览：共检测到 128 个威胁事件\n涉及 8 种不同类型的威胁\n分类准确率: 68.8%\n\n主要威胁类型分布：\n- XSS Attack: 25次 (19.53%), 严重程度: critical, 平均置信度: 0.72\n- DDoS: 24次 (18.75%), 严重程度: critical, 平均置信度: 0.754\n- Reconnaissance: 22次 (17.19%), 严重程度: low, 平均置信度: 0.868\n- Brute Force: 16次 (12.5%), 严重程度: critical, 平均置信度: 0.85\n- SQL Injection: 16次 (12.5%), 严重程度: critical, 平均置信度: 0.656\n\n严重程度分布：\n- critical: 81次 (63.28%)\n- high: 25次 (19.53%)\n- low: 22次 (17.19%)", "classification_stats": {"DDoS": {"count": 24, "percentage": 18.75, "severity": "critical", "average_confidence": 0.754, "sample_description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [64231]\nDestination Port: [3690]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack targeting Ubuntu16 was detected. It primarily involves sending numerous packets to overwhelm the target machine, utilizing resources and achieving a denial of service."}, "DoS": {"count": 12, "percentage": 9.38, "severity": "high", "average_confidence": 0.633, "sample_description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [53058]\nDestination Port: [80]\nProtocol: TCP\nMethod: [Slowloris DoS Attack]\nDescription: A [Slowloris DoS attack] was detected, which attempts to keep many connections to the target web server open and hold them open as long as possible. The source is sending small pieces of data periodically to [*************] to accomplish this."}, "XSS Attack": {"count": 25, "percentage": 19.53, "severity": "critical", "average_confidence": 0.72, "sample_description": "Source IP: [************]\nDestination IP: [**************]\nSource Port: [54122]\nDestination Port: [444]\nProtocol: [TCP]\nMethod: [Possible Meta exploit activity from Kali to Windows Vista]\nCategory: [Attempted Administrator Privilege Gain]\nDescription: A potential meta exploit was detected indicating an attempted privilege escalation from a Kali Linux system to a target running Windows Vista. This activity suggests an exploit targeted to gain administrator privileges on the system. Attack details include the use of high-risk TCP ports, and the alert was categorized under severe threats."}, "Reconnaissance": {"count": 22, "percentage": 17.19, "severity": "low", "average_confidence": 0.868, "sample_description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [1439]\nDestination Port: [445]\nProtocol: [TCP]\nAction: [allowed]\nMethod: [Internal port scanning/Nmap usage]\nPayload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAAjVEAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAAChXzIvHhtSeAAAAACNUQAAAQAN/wAAAP//AQABAAAAAAAYABgAAAAAAFAAAABKAJIlwCfoj636SXR9y9vlmmhlhkuOHarbvJIlwCfoj636SXR9y9vlmmhlhkuOHarbvGd1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAU/9TTUJzcgAAwJhFYAAAoV8yLx4bUngAAAAAjVEAAAEADf8AAAD//wEAAQAAAAAAAQAAAAAAAABQAAAAFgAAAABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAAAAAAAAAAAAAAAAAjVEACAEAAv8AAAAAAA==]\nDescription: An internal port scanning using [Nmap] from a machine identified as Windows Vista was detected. This scanning attempted to identify services running on the target machine by sending specially crafted packets to SMB service. The payload suggests utilization of standard Nmap SMB probes to ascertain information such as supports, shares, and services, which typically indicate reconnaissance activity by a potential attacker."}, "Brute Force": {"count": 16, "percentage": 12.5, "severity": "critical", "average_confidence": 0.85, "sample_description": "Flow ID: 2227662055105263\nEvent Type: alert\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [52156]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. The attack involves multiple login attempts using the [USER] command followed by [PASS] with various simple passwords. The detected sequence of commands includes [USER iscxtap] followed by [PASS 0000.00001], [PASS _0000.7227545yfnfif], and [PASS 0000.browning]. This behavior indicates an attempt to gain unauthorized access to the FTP server."}, "Spoofing": {"count": 12, "percentage": 9.38, "severity": "high", "average_confidence": 0.9, "sample_description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: blocked\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack was detected where an unauthorized ARP response was sent from [*************]. The attack aimed to mislead the network by associating the attacker's MAC address with the IP address of another host, potentially redirecting traffic meant for that host to the attacker instead."}, "Infiltration": {"count": 1, "percentage": 0.78, "severity": "high", "average_confidence": 0.4, "sample_description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [44548]\nDestination Port: [15004]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A [Port Scan] attack from a [compromised Vista machine] was detected. This method often involves sending packets to a range of ports on a single machine to discover services that can be exploited to gain unauthorized access to compute resources."}, "SQL Injection": {"count": 16, "percentage": 12.5, "severity": "critical", "average_confidence": 0.656, "sample_description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=TrackingId=x%27||pg_sleep(10)--]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been detected, exploiting the \"id\" parameter. The attack utilizes the PostgreSQL-specific function [pg_sleep(10)] to deliberately delay the response, thus confirming SQL vulnerability through time observation. This type of attack aims to create a time-based SQL injection point where the response time indicates successful SQL query manipulation, leading to potential unauthorized data access or system manipulation."}}, "threat_summaries": [{"id": "threat_0001", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:17", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "关键词: overwhelm", "攻击方法: loit attack", "指标: numerous packets", "指标: overwhelm", "指标: denial of service"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0002", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:17", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: distributed denial", "关键词: loit", "关键词: flood", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm", "指标: denial of service"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0003", "classified_type": "DDoS", "confidence": 0.8, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:22", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "攻击方法: loit attack", "指标: high volume"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0004", "classified_type": "DDoS", "confidence": 0.7, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:27", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "攻击方法: loit attack"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0005", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:28", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm", "指标: denial of service"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0006", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:28", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0007", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:30", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: distributed denial", "关键词: loit", "关键词: flood", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm", "指标: denial of service"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0008", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:31", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0009", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:35", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: distributed denial", "关键词: loit", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm", "指标: denial of service"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0010", "classified_type": "DDoS", "confidence": 0.7, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:39", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "攻击方法: loit attack"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0011", "classified_type": "DDoS", "confidence": 1.0, "severity": "critical", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:39", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm", "指标: denial of service"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0012", "classified_type": "DDoS", "confidence": 1.0, "severity": "critical", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:39", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: distributed denial", "关键词: loit", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm", "指标: denial of service"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0013", "classified_type": "DDoS", "confidence": 0.8, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:39", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "攻击方法: loit attack", "指标: denial of service"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0014", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:40", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0015", "classified_type": "DDoS", "confidence": 0.8, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:44", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "攻击方法: loit attack", "指标: denial of service"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0016", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: LOIT攻击", "timestamp": "2024-04-13 09:30:44", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: loit", "关键词: overwhelm", "攻击方法: loit attack", "指标: overwhelm"], "original_type": "DDoS", "classification_correct": true}, {"id": "threat_0017", "classified_type": "DoS", "confidence": 0.5, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-13 13:26:36", "layer": "ground", "matched_indicators": ["关键词: dos", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0018", "classified_type": "DoS", "confidence": 0.5, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-13 13:26:36", "layer": "ground", "matched_indicators": ["关键词: dos", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0019", "classified_type": "DoS", "confidence": 0.7, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: GoldenEye攻击", "timestamp": "2024-04-13 13:26:36", "layer": "ground", "matched_indicators": ["关键词: dos", "关键词: service disruption", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0020", "classified_type": "DDoS", "confidence": 0.5, "severity": "critical", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: Golden<PERSON>ye攻击", "timestamp": "2024-04-13 13:26:36", "layer": "ground", "matched_indicators": ["关键词: goldeneye", "关键词: overwhelm", "指标: overwhelm"], "original_type": "DoS", "classification_correct": false}, {"id": "threat_0021", "classified_type": "DoS", "confidence": 0.5, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-13 13:26:39", "layer": "ground", "matched_indicators": ["关键词: dos", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0022", "classified_type": "DoS", "confidence": 0.7, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: GoldenEye攻击", "timestamp": "2024-04-13 13:26:39", "layer": "ground", "matched_indicators": ["关键词: dos", "关键词: denial of service", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0023", "classified_type": "DoS", "confidence": 0.5, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-13 13:26:41", "layer": "ground", "matched_indicators": ["关键词: dos", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0024", "classified_type": "DoS", "confidence": 0.7, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-13 13:26:41", "layer": "ground", "matched_indicators": ["关键词: dos", "关键词: denial of service", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0025", "classified_type": "DDoS", "confidence": 0.5, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: Golden<PERSON>ye攻击", "timestamp": "2024-04-13 13:26:42", "layer": "ground", "matched_indicators": ["关键词: goldeneye", "关键词: overwhelm", "指标: overwhelm"], "original_type": "DoS", "classification_correct": false}, {"id": "threat_0026", "classified_type": "DoS", "confidence": 0.7, "severity": "medium", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: GoldenEye攻击", "timestamp": "2024-04-13 13:26:45", "layer": "ground", "matched_indicators": ["关键词: dos", "关键词: denial of service", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0027", "classified_type": "DoS", "confidence": 0.7, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-13 13:26:45", "layer": "ground", "matched_indicators": ["关键词: dos", "关键词: denial of service", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0028", "classified_type": "DoS", "confidence": 0.7, "severity": "high", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: GoldenEye攻击", "timestamp": "2024-04-13 13:26:47", "layer": "ground", "matched_indicators": ["关键词: dos", "关键词: denial of service", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0029", "classified_type": "DoS", "confidence": 0.7, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-13 13:26:47", "layer": "ground", "matched_indicators": ["关键词: dos", "关键词: denial of service", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0030", "classified_type": "DDoS", "confidence": 0.5, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: Golden<PERSON>ye攻击", "timestamp": "2024-04-13 13:26:51", "layer": "ground", "matched_indicators": ["关键词: goldeneye", "关键词: overwhelm", "指标: overwhelm"], "original_type": "DoS", "classification_correct": false}, {"id": "threat_0031", "classified_type": "DDoS", "confidence": 1.0, "severity": "high", "summary": "DDoS攻击 - 涉及IP: **********, *************; 协议: TCP; 方法: Golden<PERSON>ye攻击", "timestamp": "2024-04-13 13:26:51", "layer": "ground", "matched_indicators": ["关键词: ddos", "关键词: distributed denial", "关键词: goldeneye", "关键词: overwhelm", "指标: overwhelm", "指标: denial of service"], "original_type": "DoS", "classification_correct": false}, {"id": "threat_0032", "classified_type": "DoS", "confidence": 0.7, "severity": "low", "summary": "DoS攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-13 13:26:53", "layer": "ground", "matched_indicators": ["关键词: dos", "关键词: denial of service", "攻击方法: dos attack"], "original_type": "DoS", "classification_correct": true}, {"id": "threat_0033", "classified_type": "DDoS", "confidence": 0.2, "severity": "critical", "summary": "DDoS攻击 - 涉及IP: **************, ************; 协议: TCP", "timestamp": "2024-04-13 19:43:22", "layer": "ground", "matched_indicators": ["关键词: loit"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0034", "classified_type": "XSS Attack", "confidence": 0.4, "severity": "critical", "summary": "XSS Attack攻击 - 涉及IP: **************, ************; 协议: TCP", "timestamp": "2024-04-13 19:43:25", "layer": "ground", "matched_indicators": ["关键词: script", "关键词: alert"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0035", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, ************; 协议: TCP", "timestamp": "2024-04-13 19:43:25", "layer": "ground", "matched_indicators": ["关键词: recon", "关键词: reconnaissance", "关键词: scanning", "关键词: probe", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "攻击方法: reconnaissance", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0036", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: *************, ************; 协议: TCP", "timestamp": "2024-04-13 19:43:30", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: probe", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0037", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:30", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: probe", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0038", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:33", "layer": "ground", "matched_indicators": ["关键词: recon", "关键词: reconnaissance", "关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "攻击方法: reconnaissance", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0039", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: *************, ************; 协议: TCP", "timestamp": "2024-04-13 19:43:38", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "攻击方法: network scanning", "指标: scanning", "指标: probing"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0040", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:40", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0041", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:44", "layer": "ground", "matched_indicators": ["关键词: recon", "关键词: reconnaissance", "关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "攻击方法: reconnaissance", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0042", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:44", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0043", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:49", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0044", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:51", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "攻击方法: network scanning", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0045", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:56", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0046", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:56", "layer": "ground", "matched_indicators": ["关键词: recon", "关键词: reconnaissance", "关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "攻击方法: reconnaissance", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0047", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-13 19:43:56", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0048", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, ************; 协议: TCP", "timestamp": "2024-04-13 19:43:57", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: nmap", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Infiltration", "classification_correct": false}, {"id": "threat_0049", "classified_type": "Brute Force", "confidence": 0.9, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:27:50", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "关键词: login", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0050", "classified_type": "Brute Force", "confidence": 0.9, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:27:53", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "关键词: login", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0051", "classified_type": "Brute Force", "confidence": 1.0, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:27:53", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "关键词: login", "关键词: credential", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0052", "classified_type": "Brute Force", "confidence": 0.7, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:27:53", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: login", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0053", "classified_type": "Brute Force", "confidence": 0.7, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:27:53", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: authentication", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0054", "classified_type": "Brute Force", "confidence": 0.7, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:27:53", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0055", "classified_type": "Brute Force", "confidence": 0.9, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:27:58", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "关键词: login", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0056", "classified_type": "Brute Force", "confidence": 0.9, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:28:01", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "关键词: login", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0057", "classified_type": "Brute Force", "confidence": 0.9, "severity": "critical", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:28:01", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: login", "关键词: credential", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0058", "classified_type": "Brute Force", "confidence": 0.9, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:28:01", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "关键词: login", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0059", "classified_type": "Brute Force", "confidence": 0.9, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:28:05", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "关键词: login", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0060", "classified_type": "Brute Force", "confidence": 1.0, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:28:05", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "关键词: credential", "攻击方法: brute force attack", "指标: multiple attempts"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0061", "classified_type": "Brute Force", "confidence": 0.7, "severity": "critical", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:28:05", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: login", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0062", "classified_type": "Brute Force", "confidence": 0.9, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:28:05", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "关键词: login", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0063", "classified_type": "Brute Force", "confidence": 0.7, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:28:06", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: password", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0064", "classified_type": "Brute Force", "confidence": 0.9, "severity": "medium", "summary": "Brute Force攻击 - 涉及IP: **********, *************; 协议: TCP", "timestamp": "2024-04-14 06:28:08", "layer": "ground", "matched_indicators": ["关键词: brute force", "关键词: login", "关键词: credential", "攻击方法: brute force attack"], "original_type": "Brute Force", "classification_correct": true}, {"id": "threat_0065", "classified_type": "Spoofing", "confidence": 1.0, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:27", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "关键词: mac address", "攻击方法: spoofing attack", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0066", "classified_type": "Spoofing", "confidence": 0.9, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:30", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "关键词: mac address", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0067", "classified_type": "XSS Attack", "confidence": 0.2, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:35", "layer": "ground", "matched_indicators": ["关键词: script"], "original_type": "Spoofing", "classification_correct": false}, {"id": "threat_0068", "classified_type": "Spoofing", "confidence": 1.0, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:35", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "攻击方法: spoofing attack", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0069", "classified_type": "XSS Attack", "confidence": 0.4, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:35", "layer": "ground", "matched_indicators": ["关键词: script", "关键词: alert"], "original_type": "Spoofing", "classification_correct": false}, {"id": "threat_0070", "classified_type": "Spoofing", "confidence": 1.0, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:35", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "攻击方法: spoofing attack", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0071", "classified_type": "Spoofing", "confidence": 0.3, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:39", "layer": "ground", "matched_indicators": ["关键词: mac address", "指标: fake"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0072", "classified_type": "Spoofing", "confidence": 1.0, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:39", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "攻击方法: spoofing attack", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0073", "classified_type": "Spoofing", "confidence": 1.0, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:39", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "攻击方法: spoofing attack", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0074", "classified_type": "XSS Attack", "confidence": 0.2, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:42", "layer": "ground", "matched_indicators": ["关键词: script"], "original_type": "Spoofing", "classification_correct": false}, {"id": "threat_0075", "classified_type": "Spoofing", "confidence": 0.9, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:42", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "关键词: mac address", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0076", "classified_type": "Spoofing", "confidence": 1.0, "severity": "high", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:42", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "攻击方法: spoofing attack", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0077", "classified_type": "Spoofing", "confidence": 1.0, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:44", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "攻击方法: spoofing attack", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0078", "classified_type": "Spoofing", "confidence": 1.0, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:44", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "关键词: mac address", "攻击方法: spoofing attack", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0079", "classified_type": "XSS Attack", "confidence": 0.4, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:47", "layer": "ground", "matched_indicators": ["关键词: script", "关键词: alert"], "original_type": "Spoofing", "classification_correct": false}, {"id": "threat_0080", "classified_type": "Spoofing", "confidence": 0.7, "severity": "medium", "summary": "Spoofing攻击 - 涉及IP: ***********, *************", "timestamp": "2024-04-14 21:04:50", "layer": "ground", "matched_indicators": ["关键词: spoofing", "关键词: arp spoofing", "攻击方法: arp spoofing"], "original_type": "Spoofing", "classification_correct": true}, {"id": "threat_0081", "classified_type": "XSS Attack", "confidence": 0.4, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: ************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:03", "layer": "ground", "matched_indicators": ["关键词: script", "关键词: alert"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0082", "classified_type": "Infiltration", "confidence": 0.4, "severity": "high", "summary": "Infiltration攻击 - 涉及IP: ************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:03", "layer": "ground", "matched_indicators": ["关键词: unauthorized access", "指标: unauthorized", "指标: compromise"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0083", "classified_type": "Reconnaissance", "confidence": 0.4, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:03", "layer": "ground", "matched_indicators": ["关键词: probe", "关键词: port scan"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0084", "classified_type": "Reconnaissance", "confidence": 1.0, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:08", "layer": "ground", "matched_indicators": ["关键词: recon", "关键词: reconnaissance", "关键词: scanning", "关键词: port scan", "攻击方法: port scanning", "攻击方法: reconnaissance", "指标: scanning"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0085", "classified_type": "DDoS", "confidence": 0.2, "severity": "high", "summary": "DDoS攻击 - 涉及IP: ************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:08", "layer": "ground", "matched_indicators": ["关键词: loit"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0086", "classified_type": "Reconnaissance", "confidence": 0.9, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-15 08:52:08", "layer": "ground", "matched_indicators": ["关键词: recon", "关键词: reconnaissance", "关键词: port scan", "攻击方法: reconnaissance"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0087", "classified_type": "Reconnaissance", "confidence": 0.5, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: *************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:08", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: port scan", "指标: scanning"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0088", "classified_type": "Reconnaissance", "confidence": 0.3, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: *************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:13", "layer": "ground", "matched_indicators": ["关键词: port scan", "指标: probing"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0089", "classified_type": "XSS Attack", "confidence": 0.2, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: ************, *************; 协议: TCP", "timestamp": "2024-04-15 08:52:13", "layer": "ground", "matched_indicators": ["关键词: script"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0090", "classified_type": "DDoS", "confidence": 0.2, "severity": "high", "summary": "DDoS攻击 - 涉及IP: *************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:14", "layer": "ground", "matched_indicators": ["关键词: loit"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0091", "classified_type": "Reconnaissance", "confidence": 0.8, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: *************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:19", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0092", "classified_type": "XSS Attack", "confidence": 0.2, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:22", "layer": "ground", "matched_indicators": ["关键词: script"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0093", "classified_type": "Reconnaissance", "confidence": 0.8, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, ************1; 协议: TCP", "timestamp": "2024-04-15 08:52:23", "layer": "ground", "matched_indicators": ["关键词: scanning", "关键词: port scan", "攻击方法: port scanning", "指标: scanning"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0094", "classified_type": "Reconnaissance", "confidence": 0.4, "severity": "low", "summary": "Reconnaissance攻击 - 涉及IP: ************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:23", "layer": "ground", "matched_indicators": ["关键词: probe", "关键词: port scan"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0095", "classified_type": "DDoS", "confidence": 0.2, "severity": "high", "summary": "DDoS攻击 - 涉及IP: ************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:23", "layer": "ground", "matched_indicators": ["关键词: loit"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0096", "classified_type": "XSS Attack", "confidence": 0.2, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *************, ************; 协议: TCP", "timestamp": "2024-04-15 08:52:26", "layer": "ground", "matched_indicators": ["关键词: script"], "original_type": "Recon", "classification_correct": false}, {"id": "threat_0097", "classified_type": "SQL Injection", "confidence": 0.4, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:41", "layer": "ground", "matched_indicators": ["关键词: sql injection", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0098", "classified_type": "SQL Injection", "confidence": 0.4, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:41", "layer": "ground", "matched_indicators": ["关键词: sql injection", "指标: database", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0099", "classified_type": "SQL Injection", "confidence": 0.6, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:41", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: select", "指标: database", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0100", "classified_type": "SQL Injection", "confidence": 0.4, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "指标: database", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0101", "classified_type": "SQL Injection", "confidence": 1.0, "severity": "critical", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: union", "关键词: select", "关键词: insert", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0102", "classified_type": "SQL Injection", "confidence": 0.5, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0103", "classified_type": "SQL Injection", "confidence": 0.5, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0104", "classified_type": "SQL Injection", "confidence": 0.8, "severity": "critical", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: union", "关键词: select", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0105", "classified_type": "SQL Injection", "confidence": 0.7, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: select", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0106", "classified_type": "SQL Injection", "confidence": 0.7, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: select", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0107", "classified_type": "SQL Injection", "confidence": 0.7, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: insert", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0108", "classified_type": "SQL Injection", "confidence": 0.7, "severity": "critical", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: select", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0109", "classified_type": "SQL Injection", "confidence": 0.6, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: select", "指标: database", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0110", "classified_type": "SQL Injection", "confidence": 0.7, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:42", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: select", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0111", "classified_type": "SQL Injection", "confidence": 0.9, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: *********, ***********, **********", "timestamp": "2024-04-15 22:55:45", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: union", "关键词: select", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0112", "classified_type": "SQL Injection", "confidence": 0.9, "severity": "high", "summary": "SQL Injection攻击 - 涉及IP: ***********, **********", "timestamp": "2024-04-15 22:55:47", "layer": "ground", "matched_indicators": ["关键词: sql injection", "关键词: union", "关键词: select", "指标: database", "指标: query", "指标: injection"], "original_type": "SQL Injection", "classification_correct": true}, {"id": "threat_0113", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "high", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:46", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0114", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "high", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:46", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0115", "classified_type": "XSS Attack", "confidence": 0.4, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:48", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: script"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0116", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:53", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0117", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:53", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0118", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:58", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0119", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:58", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0120", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:58", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0121", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:58", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0122", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:59", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "攻击方法: xss attack", "攻击方法: cross-site scripting", "指标: malicious script"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0123", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:21:59", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0124", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:22:03", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0125", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:22:06", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0126", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "high", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:22:06", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0127", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:22:06", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting"], "original_type": "XSS Attack", "classification_correct": true}, {"id": "threat_0128", "classified_type": "XSS Attack", "confidence": 1.0, "severity": "medium", "summary": "XSS Attack攻击 - 涉及IP: *********, ***********, ***********", "timestamp": "2024-04-16 11:22:06", "layer": "ground", "matched_indicators": ["关键词: xss", "关键词: cross-site scripting", "关键词: script", "关键词: javascript", "关键词: alert", "攻击方法: xss attack", "攻击方法: cross-site scripting", "指标: malicious script"], "original_type": "XSS Attack", "classification_correct": true}], "total_threats": 128, "unique_threat_types": 8, "classification_accuracy": 0.688, "processing_time": "2025-07-24T14:20:27.717148", "model_used": "rule_based_classifier"}