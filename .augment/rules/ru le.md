---
inclusion: always
type: "manual"
---
# 中文交流规则

## 语言使用规范

在本项目中，请遵循以下语言使用规范：

### 代码和技术实现
- 所有代码必须使用英文编写
- 变量名、函数名、类名等标识符使用英文
- 代码注释，打印输出使用英文

### 交流和文档
- 所有对话交流使用中文
- 需求文档、设计文档、任务文档使用中文
- 用户故事和验收标准使用中文
- 错误信息和日志可以使用中文

### 示例格式

```python
class ThreatAnalyzer:
    """威胁分析器类"""
    
    def analyze_threat(self, threat_data):
        """分析威胁数据并返回结果"""
        # 处理威胁数据
        result = self.process_data(threat_data)
        return result
```

### 文档结构
- 标题和章节标题使用中文
- 技术术语可以保留英文原文，但需要中文解释
- 代码示例中的注释使用中文

这个规则确保团队成员能够更好地理解和协作，同时保持代码的国际化标准。