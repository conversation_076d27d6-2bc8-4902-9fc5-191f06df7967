import json
import os
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path
import logging
from abc import ABC, abstractmethod

import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import openai
import pandas as pd
from datetime import datetime

# 导入统一的模型配置
from model_config import ModelConfig, get_global_config, get_global_llm, LLMInterface

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SummarizationConfig:
    """Summarization Agent特定配置类"""
    batch_size: int = 4
    output_dir: str = "./summarization_outputs"
    
    def __post_init__(self):
        """确保输出目录存在"""
        os.makedirs(self.output_dir, exist_ok=True)

class SummarizationLLMWrapper:
    """Summarization Agent的LLM包装器，使用统一的LLM接口"""
    
    def __init__(self, config: SummarizationConfig):
        self.config = config
        self.llm = get_global_llm()
        logger.info("Initialized SummarizationLLMWrapper with unified LLM interface")
    
    def generate_response(self, prompt: str) -> str:
        """生成单个响应"""
        # 检查是否为测试模式
        global_config = get_global_config()
        if global_config.test_mode:
            return self._test_mode_response(prompt)
        else:
            return self.llm.generate_response(prompt)
    
    def _test_mode_response(self, prompt: str) -> str:
        """测试模式响应 - 基于关键词匹配进行威胁分类"""
        prompt_lower = prompt.lower()
        
        # 威胁分类关键词匹配 - 增强版
        if "ddos" in prompt_lower or "distributed denial" in prompt_lower or "loit" in prompt_lower or "goldeneye" in prompt_lower:
            return """
Attack Types: [DDoS Attack]
Key IoCs: [High volume traffic, Multiple source IPs, Service disruption, LOIT/GoldenEye attack patterns]
Impact Assessment: [High - Service availability compromised, Network infrastructure at risk]
Attack Patterns: [Coordinated attack from multiple sources, Application layer attacks]
Mitigation Recommendations: [Implement rate limiting, Deploy DDoS protection, Monitor traffic patterns, Configure load balancing]
Confidence: 0.95
"""
        elif "malware" in prompt_lower or "virus" in prompt_lower or "trojan" in prompt_lower:
            return """
Attack Types: [Malware Infection]
Key IoCs: [Suspicious file execution, Unauthorized network connections, System performance degradation]
Impact Assessment: [High - System integrity and data security at risk]
Attack Patterns: [File-based attack vector, Persistence mechanisms, Lateral movement]
Mitigation Recommendations: [Update antivirus signatures, Implement application whitelisting, Network segmentation, Endpoint detection]
Confidence: 0.90
"""
        elif "phishing" in prompt_lower or "social engineering" in prompt_lower:
            return """
Attack Types: [Phishing Attack]
Key IoCs: [Suspicious email attachments, Credential harvesting attempts, Social engineering tactics]
Impact Assessment: [Medium - User credentials and sensitive data at risk]
Attack Patterns: [Email-based attack vector, User interaction required, Credential theft]
Mitigation Recommendations: [User security training, Email filtering, Multi-factor authentication, Email security gateway]
Confidence: 0.85
"""
        elif "sql injection" in prompt_lower or "injection" in prompt_lower:
            return """
Attack Types: [SQL Injection]
Key IoCs: [Malicious SQL queries, Database access attempts, Data exfiltration]
Impact Assessment: [Critical - Database integrity and confidentiality compromised]
Attack Patterns: [Web application vulnerability exploitation, Database manipulation]
Mitigation Recommendations: [Input validation, Parameterized queries, Web application firewall, Database monitoring]
Confidence: 0.92
"""
        elif "brute force" in prompt_lower or "password attack" in prompt_lower:
            return """
Attack Types: [Brute Force Attack]
Key IoCs: [Multiple failed login attempts, Dictionary attacks, Credential stuffing]
Impact Assessment: [Medium - Account security at risk]
Attack Patterns: [Automated password guessing, Account enumeration, Credential attacks]
Mitigation Recommendations: [Account lockout policies, Strong password requirements, Rate limiting, Multi-factor authentication]
Confidence: 0.88
"""
        elif "infiltration" in prompt_lower or "penetration" in prompt_lower:
            return """
Attack Types: [Infiltration Attack]
Key IoCs: [Unauthorized access attempts, Privilege escalation, System compromise]
Impact Assessment: [High - System security compromised, Data breach risk]
Attack Patterns: [Multi-stage attack, Persistence establishment, Lateral movement]
Mitigation Recommendations: [Access control hardening, Network monitoring, Intrusion detection, Security patching]
Confidence: 0.87
"""
        elif "spoofing" in prompt_lower or "arp" in prompt_lower or "dns" in prompt_lower:
            return """
Attack Types: [Spoofing Attack]
Key IoCs: [ARP/DNS spoofing, IP address manipulation, Network traffic redirection]
Impact Assessment: [Medium - Network integrity and confidentiality at risk]
Attack Patterns: [Network protocol exploitation, Man-in-the-middle attacks]
Mitigation Recommendations: [Network segmentation, Protocol security, Monitoring tools, DNS security]
Confidence: 0.83
"""
        else:
            return """
Attack Types: [General Security Threat]
Key IoCs: [Suspicious network activity, Anomalous system behavior, Unknown attack patterns]
Impact Assessment: [Medium - Potential security risk identified, requires analysis]
Attack Patterns: [Unknown attack vector, requires further investigation]
Mitigation Recommendations: [Enhanced monitoring, Security assessment, Incident response preparation, Threat analysis]
Confidence: 0.60
"""
    
    def batch_generate(self, prompts: List[str]) -> List[str]:
        """批量生成响应"""
        responses = []
        for i in range(0, len(prompts), self.config.batch_size):
            batch = prompts[i:i + self.config.batch_size]
            batch_responses = [self.generate_response(prompt) for prompt in batch]
            responses.extend(batch_responses)
        return responses



class SummarizationAgent:
    """信息摘要Agent类"""
    
    def __init__(self, config: SummarizationConfig):
        self.config = config
        self.llm = self._initialize_llm()
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 摘要提示模板
        self.summarization_prompt_template = """
You are a cybersecurity expert specializing in threat analysis for Space-Air-Ground Integrated Networks (SAGIN). 
Your task is to analyze and summarize security threat logs to identify key attack patterns and vulnerabilities.

Please analyze the following security logs and provide a comprehensive summary:

=== SECURITY LOGS ===
{logs}

=== ANALYSIS REQUIREMENTS ===
1. Identify the main types of attacks mentioned in the logs
2. Extract key indicators of compromise (IoCs)
3. Assess the potential impact on SAGIN infrastructure
4. Highlight any patterns or correlations between different attacks
5. Provide actionable insights for threat mitigation

=== OUTPUT FORMAT ===
Please structure your response as follows:
- Attack Types: [List main attack categories]
- Key IoCs: [Critical indicators found]
- Impact Assessment: [Potential consequences]
- Attack Patterns: [Observed correlations]
- Mitigation Recommendations: [Actionable security measures]

Analysis:
"""
    
    def _initialize_llm(self) -> SummarizationLLMWrapper:
        """初始化LLM模型包装器"""
        return SummarizationLLMWrapper(self.config)
    
    def load_dataset(self, file_path: str) -> List[Dict[str, Any]]:
        """加载数据集 - 支持数组格式"""
        logger.info(f"Loading dataset from: {file_path}")
        
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"Dataset file not found: {file_path}")
        
        # 支持多种数据格式
        if file_path.suffix == '.json':
            with open(file_path, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
                
            # 处理数组格式数据集
            data = self._parse_array_format(raw_data)
            
        elif file_path.suffix == '.csv':
            df = pd.read_csv(file_path)
            data = df.to_dict('records')
        elif file_path.suffix == '.txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                data = [{'log_entry': line.strip()} for line in lines if line.strip()]
        else:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")
        
        logger.info(f"Loaded {len(data)} records")
        return data
    
    def load_unclassified_threat_data(self, file_path: str = "output_0515_dataset_fin.json") -> List[Dict[str, Any]]:
        """加载未分类的威胁数据 - 任务1实现：模拟去掉威胁类型标签"""
        logger.info(f"正在加载未分类威胁数据: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            # 解析数组格式数据，模拟去掉索引3的威胁类型，只保留索引5的威胁描述
            unclassified_data = []
            for i, record in enumerate(raw_data):
                if len(record) >= 8:  # 确保数组格式正确
                    # 模拟未分类数据：不使用索引3的威胁类型
                    description = record[5]  # 索引5: 威胁描述
                    
                    threat_item = {
                        'id': f'threat_{i+1:04d}',
                        'description': description,  # 主要用于分类的数据
                        'timestamp': record[4],
                        'layer': record[1],
                        'network_type': record[2],
                        'tag': record[6],
                        'is_valid': record[0],
                        'status': record[7],
                        'raw_record': record,
                        # 保留原始威胁类型用于验证（实际场景中不会有）
                        '_original_threat_type': record[3]  # 仅用于验证分类准确性
                    }
                    unclassified_data.append(threat_item)
                else:
                    logger.warning(f"跳过格式错误的记录，索引 {i}: {record}")
            
            logger.info(f"成功加载 {len(unclassified_data)} 条未分类威胁数据")
            return unclassified_data
            
        except FileNotFoundError:
            logger.error(f"威胁数据文件未找到: {file_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            raise
        except Exception as e:
            logger.error(f"加载威胁数据时发生错误: {e}")
            raise
    
    def _parse_array_format(self, raw_data: List[List]) -> List[Dict[str, Any]]:
        """解析数组格式的威胁数据 - 增强版"""
        parsed_data = []
        
        for i, record in enumerate(raw_data):
            if len(record) >= 8:  # 确保格式正确
                parsed_record = {
                    'id': f'threat_{i+1:04d}',
                    'is_valid': record[0],
                    'layer': record[1],  # ground, space, air
                    'network_type': record[2],
                    'threat_category': record[3],
                    'timestamp': record[4],
                    'description': record[5],
                    'tag': record[6],
                    'status': record[7],
                    'severity': self._calculate_severity(record[3], record[5]),
                    'attack_type': self._extract_attack_type(record[3], record[5]),
                    'source_ip': self._extract_ip(record[5], 'source'),
                    'destination_ip': self._extract_ip(record[5], 'destination'),
                    'protocol': self._extract_protocol(record[5]),
                    'raw_record': record  # 保留原始数据
                }
                parsed_data.append(parsed_record)
            else:
                logger.warning(f"Skipping malformed record at index {i}: {record}")
        
        return parsed_data
    
    def _calculate_severity(self, threat_category: str, description: str) -> str:
        """根据威胁类型和描述计算严重性"""
        severity_map = {
            'DDoS': 'high',
            'DoS': 'high',
            'SQL Injection': 'critical',
            'XSS Attack': 'medium',
            'Brute Force': 'medium',
            'Infiltration': 'high',
            'Spoofing': 'medium',
            'Recon': 'low'
        }
        
        # 检查描述中的关键词
        desc_lower = description.lower()
        if any(word in desc_lower for word in ['critical', 'severe', 'immediate', 'administrator']):
            return 'critical'
        elif any(word in desc_lower for word in ['high', 'major', 'significant']):
            return 'high'
        elif any(word in desc_lower for word in ['medium', 'moderate']):
            return 'medium'
            
        return severity_map.get(threat_category, 'low')
    
    def _extract_attack_type(self, threat_category: str, description: str) -> str:
        """提取具体的攻击类型"""
        desc_lower = description.lower()
        
        attack_types = {
            'ddos': ['loit', 'goldeneye', 'hulk', 'slowloris', 'slowhttptest'],
            'sql_injection': ['union', 'select', 'insert', 'update', 'delete'],
            'xss': ['script', 'alert', 'javascript', 'onerror', 'onload'],
            'brute_force': ['password', 'login', 'authentication', 'credential'],
            'spoofing': ['arp', 'dns', 'ip spoofing', 'mac address'],
            'recon': ['nmap', 'port scan', 'scanning', 'probe']
        }
        
        for attack_type, keywords in attack_types.items():
            if any(keyword in desc_lower for keyword in keywords):
                return attack_type
                
        return threat_category.lower().replace(' ', '_')
    
    def preprocess_logs(self, logs: List[Dict[str, Any]]) -> List[str]:
        """预处理日志数据"""
        processed_logs = []
        
        for log in logs:
            # 提取关键字段
            if isinstance(log, dict):
                # 如果是字典格式，提取主要字段
                log_text = ""
                for key, value in log.items():
                    if key in ['timestamp', 'source', 'destination', 'attack_type', 
                              'severity', 'description', 'log_entry']:
                        log_text += f"{key}: {value}\n"
                processed_logs.append(log_text.strip())
            else:
                # 如果是字符串格式，直接使用
                processed_logs.append(str(log))
        
        return processed_logs
    
    def chunk_logs(self, logs: List[str], chunk_size: int = 5) -> List[List[str]]:
        """将日志分块处理"""
        chunks = []
        for i in range(0, len(logs), chunk_size):
            chunk = logs[i:i + chunk_size]
            chunks.append(chunk)
        return chunks
    
    def classify_threats_from_description(self, description: str) -> Dict[str, Any]:
        """从威胁描述中分析和判断威胁类型 - 任务1核心功能"""
        
        # 威胁类型识别规则（基于关键词和模式）
        threat_patterns = {
            'DDoS': {
                'keywords': ['ddos', 'distributed denial', 'loit', 'goldeneye', 'hulk', 'slowloris', 'slowhttptest', 'flood', 'overwhelm'],
                'methods': ['loit attack', 'goldeneye attack', 'hulk attack', 'slowloris attack'],
                'indicators': ['numerous packets', 'high volume', 'overwhelm', 'denial of service', 'target machine']
            },
            'DoS': {
                'keywords': ['dos', 'denial of service', 'service disruption'],
                'methods': ['dos attack'],
                'indicators': ['service unavailable', 'resource exhaustion']
            },
            'SQL Injection': {
                'keywords': ['sql injection', 'union', 'select', 'insert', 'update', 'delete', 'drop table'],
                'methods': ['sql injection attack'],
                'indicators': ['database', 'query', 'injection']
            },
            'XSS Attack': {
                'keywords': ['xss', 'cross-site scripting', 'script', 'javascript', 'alert'],
                'methods': ['xss attack', 'cross-site scripting'],
                'indicators': ['script injection', 'malicious script']
            },
            'Brute Force': {
                'keywords': ['brute force', 'password', 'login', 'authentication', 'credential'],
                'methods': ['brute force attack', 'password attack'],
                'indicators': ['multiple attempts', 'login failure', 'password guessing']
            },
            'Infiltration': {
                'keywords': ['infiltration', 'penetration', 'unauthorized access', 'breach'],
                'methods': ['infiltration attack'],
                'indicators': ['unauthorized', 'intrusion', 'compromise']
            },
            'Spoofing': {
                'keywords': ['spoofing', 'arp spoofing', 'dns spoofing', 'ip spoofing', 'mac address'],
                'methods': ['spoofing attack', 'arp spoofing', 'dns spoofing'],
                'indicators': ['fake', 'impersonation', 'masquerade']
            },
            'Reconnaissance': {
                'keywords': ['recon', 'reconnaissance', 'scanning', 'probe', 'nmap', 'port scan'],
                'methods': ['port scanning', 'network scanning', 'reconnaissance'],
                'indicators': ['scanning', 'probing', 'enumeration']
            }
        }
        
        desc_lower = description.lower()
        
        # 计算每种威胁类型的匹配分数
        threat_scores = {}
        
        for threat_type, patterns in threat_patterns.items():
            score = 0
            matched_indicators = []
            
            # 检查关键词
            for keyword in patterns['keywords']:
                if keyword in desc_lower:
                    score += 2
                    matched_indicators.append(f"关键词: {keyword}")
            
            # 检查攻击方法
            for method in patterns['methods']:
                if method in desc_lower:
                    score += 3
                    matched_indicators.append(f"攻击方法: {method}")
            
            # 检查指标
            for indicator in patterns['indicators']:
                if indicator in desc_lower:
                    score += 1
                    matched_indicators.append(f"指标: {indicator}")
            
            if score > 0:
                threat_scores[threat_type] = {
                    'score': score,
                    'matched_indicators': matched_indicators
                }
        
        # 选择得分最高的威胁类型
        if threat_scores:
            best_match = max(threat_scores.items(), key=lambda x: x[1]['score'])
            classified_type = best_match[0]
            confidence = min(best_match[1]['score'] / 10.0, 1.0)  # 归一化到0-1
            matched_indicators = best_match[1]['matched_indicators']
        else:
            # 如果没有匹配，尝试从协议和端口推断
            classified_type = self._classify_by_protocol_and_context(description)
            confidence = 0.3  # 低置信度
            matched_indicators = ["基于协议和上下文推断"]
        
        return {
            'threat_type': classified_type,
            'confidence': confidence,
            'matched_indicators': matched_indicators,
            'all_scores': threat_scores
        }
    
    def _classify_by_protocol_and_context(self, description: str) -> str:
        """基于协议和上下文进行威胁分类的后备方法"""
        desc_lower = description.lower()
        
        # 基于协议和端口的简单推断
        if 'tcp' in desc_lower and ('port' in desc_lower or 'flood' in desc_lower):
            return 'DDoS'
        elif 'http' in desc_lower or 'web' in desc_lower:
            return 'Web Attack'
        elif 'network' in desc_lower and 'traffic' in desc_lower:
            return 'Network Attack'
        else:
            return 'Unknown'
    
    def classify_and_summarize_threats(self, unclassified_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """威胁分类和摘要 - 任务1核心功能：从描述中分类威胁"""
        logger.info("开始从描述中进行威胁分类和摘要...")
        
        # 统计威胁类型
        threat_types = {}
        threat_summaries = []
        classification_accuracy = []
        
        for threat in unclassified_data:
            description = threat['description']
            
            # 从描述中分类威胁类型
            classification_result = self.classify_threats_from_description(description)
            classified_type = classification_result['threat_type']
            confidence = classification_result['confidence']
            
            # 统计威胁类型
            if classified_type not in threat_types:
                threat_types[classified_type] = {
                    'count': 0,
                    'descriptions': [],
                    'severity': 'low',
                    'total_confidence': 0.0
                }
            
            threat_types[classified_type]['count'] += 1
            threat_types[classified_type]['descriptions'].append(description)
            threat_types[classified_type]['total_confidence'] += confidence
            
            # 计算严重程度
            severity = self._calculate_threat_severity(classified_type, description)
            if self._is_higher_severity(severity, threat_types[classified_type]['severity']):
                threat_types[classified_type]['severity'] = severity
            
            # 生成单个威胁摘要
            threat_summary = {
                'id': threat['id'],
                'classified_type': classified_type,
                'confidence': round(confidence, 3),
                'severity': severity,
                'summary': self._generate_threat_summary(classified_type, description),
                'timestamp': threat['timestamp'],
                'layer': threat['layer'],
                'matched_indicators': classification_result['matched_indicators']
            }
            
            # 验证分类准确性（仅用于demo验证）
            if '_original_threat_type' in threat:
                original_type = threat['_original_threat_type']
                is_correct = (classified_type.lower() == original_type.lower() or 
                            (classified_type == 'DDoS' and original_type == 'DDoS'))
                threat_summary['original_type'] = original_type
                threat_summary['classification_correct'] = is_correct
                classification_accuracy.append(is_correct)
            
            threat_summaries.append(threat_summary)
        
        # 生成分类统计
        classification_stats = {}
        for threat_type, info in threat_types.items():
            avg_confidence = info['total_confidence'] / info['count'] if info['count'] > 0 else 0
            classification_stats[threat_type] = {
                'count': info['count'],
                'percentage': round((info['count'] / len(unclassified_data)) * 100, 2),
                'severity': info['severity'],
                'average_confidence': round(avg_confidence, 3),
                'sample_description': info['descriptions'][0] if info['descriptions'] else ""
            }
        
        # 计算总体分类准确性
        overall_accuracy = sum(classification_accuracy) / len(classification_accuracy) if classification_accuracy else 0
        
        # 生成总体摘要
        overall_summary = self._generate_classification_overview(classification_stats, len(unclassified_data), overall_accuracy)
        
        result = {
            'overall_summary': overall_summary,
            'classification_stats': classification_stats,
            'threat_summaries': threat_summaries,
            'total_threats': len(unclassified_data),
            'unique_threat_types': len(threat_types),
            'classification_accuracy': round(overall_accuracy, 3),
            'processing_time': datetime.now().isoformat(),
            'model_used': 'rule_based_classifier'
        }
        
        logger.info(f"威胁分类和摘要完成，共处理 {len(unclassified_data)} 条威胁数据，分类准确率: {overall_accuracy:.1%}")
        return result
    
    def _calculate_threat_severity(self, threat_type: str, description: str) -> str:
        """计算威胁严重程度"""
        severity_keywords = {
            'critical': ['critical', 'severe', 'immediate', 'emergency', 'administrator', 'root'],
            'high': ['high', 'major', 'significant', 'ddos', 'sql injection', 'infiltration'],
            'medium': ['medium', 'moderate', 'brute force', 'spoofing', 'xss'],
            'low': ['low', 'minor', 'recon', 'scanning', 'probe']
        }
        
        desc_lower = description.lower()
        threat_lower = threat_type.lower()
        
        # 检查描述和威胁类型中的关键词
        for severity, keywords in severity_keywords.items():
            if any(keyword in desc_lower or keyword in threat_lower for keyword in keywords):
                return severity
        
        return 'medium'  # 默认中等严重程度
    
    def _is_higher_severity(self, severity1: str, severity2: str) -> bool:
        """比较严重程度等级"""
        severity_order = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        return severity_order.get(severity1, 2) > severity_order.get(severity2, 2)
    
    def _generate_threat_summary(self, threat_type: str, description: str) -> str:
        """生成单个威胁摘要"""
        # 提取关键信息
        key_info = []
        
        # 提取IP地址
        import re
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        ips = re.findall(ip_pattern, description)
        if ips:
            key_info.append(f"涉及IP: {', '.join(set(ips))}")
        
        # 提取协议
        if 'TCP' in description:
            key_info.append("协议: TCP")
        elif 'UDP' in description:
            key_info.append("协议: UDP")
        
        # 提取攻击方法
        if 'LOIT' in description:
            key_info.append("方法: LOIT攻击")
        elif 'GoldenEye' in description:
            key_info.append("方法: GoldenEye攻击")
        
        summary = f"{threat_type}攻击"
        if key_info:
            summary += f" - {'; '.join(key_info)}"
        
        return summary
    
    def _generate_threat_overview(self, classification_stats: Dict, total_count: int) -> str:
        """生成威胁总览"""
        overview_parts = [
            f"威胁分析总览：共检测到 {total_count} 个威胁事件",
            f"涉及 {len(classification_stats)} 种不同类型的威胁"
        ]
        
        # 按数量排序威胁类型
        sorted_threats = sorted(classification_stats.items(), 
                              key=lambda x: x[1]['count'], reverse=True)
        
        overview_parts.append("主要威胁类型分布：")
        for threat_type, stats in sorted_threats[:5]:  # 显示前5种威胁
            overview_parts.append(
                f"- {threat_type}: {stats['count']}次 ({stats['percentage']}%), "
                f"严重程度: {stats['severity']}"
            )
        
        # 严重程度统计
        severity_counts = {}
        for stats in classification_stats.values():
            severity = stats['severity']
            severity_counts[severity] = severity_counts.get(severity, 0) + stats['count']
        
        if severity_counts:
            overview_parts.append("严重程度分布：")
            for severity in ['critical', 'high', 'medium', 'low']:
                if severity in severity_counts:
                    count = severity_counts[severity]
                    percentage = round((count / total_count) * 100, 2)
                    overview_parts.append(f"- {severity}: {count}次 ({percentage}%)")
        
        return "\n".join(overview_parts)
    
    def _generate_classification_overview(self, classification_stats: Dict, total_count: int, overall_accuracy: float) -> str:
        """生成威胁分类总览"""
        overview_parts = [
            f"威胁分析总览：共检测到 {total_count} 个威胁事件",
            f"涉及 {len(classification_stats)} 种不同类型的威胁",
            f"分类准确率: {overall_accuracy:.1%}"
        ]
        
        # 按数量排序威胁类型
        sorted_threats = sorted(classification_stats.items(), 
                              key=lambda x: x[1]['count'], reverse=True)
        
        overview_parts.append("\n主要威胁类型分布：")
        for threat_type, stats in sorted_threats[:5]:  # 显示前5种威胁
            overview_parts.append(
                f"- {threat_type}: {stats['count']}次 ({stats['percentage']}%), "
                f"严重程度: {stats['severity']}, 平均置信度: {stats['average_confidence']}"
            )
        
        # 严重程度统计
        severity_counts = {}
        for stats in classification_stats.values():
            severity = stats['severity']
            severity_counts[severity] = severity_counts.get(severity, 0) + stats['count']
        
        if severity_counts:
            overview_parts.append("\n严重程度分布：")
            for severity in ['critical', 'high', 'medium', 'low']:
                if severity in severity_counts:
                    count = severity_counts[severity]
                    percentage = round((count / total_count) * 100, 2)
                    overview_parts.append(f"- {severity}: {count}次 ({percentage}%)")
        
        return "\n".join(overview_parts)

    def summarize_logs(self, logs: List[str]) -> Dict[str, Any]:
        """摘要日志信息"""
        logger.info("Starting log summarization...")
        
        # 将日志分块处理
        log_chunks = self.chunk_logs(logs, chunk_size=3)
        chunk_summaries = []
        
        for i, chunk in enumerate(log_chunks):
            logger.info(f"Processing chunk {i+1}/{len(log_chunks)}")
            
            # 合并日志块
            logs_text = "\n".join(chunk)
            
            # 生成摘要提示
            prompt = self.summarization_prompt_template.format(logs=logs_text)
            
            # 生成摘要
            summary = self.llm.generate_response(prompt)
            
            chunk_summaries.append({
                'chunk_id': i + 1,
                'original_logs': chunk,
                'summary': summary,
                'timestamp': datetime.now().isoformat()
            })
        
        # 生成总体摘要
        overall_summary = self._generate_overall_summary(chunk_summaries)
        
        return {
            'overall_summary': overall_summary,
            'chunk_summaries': chunk_summaries,
            'total_logs': len(logs),
            'total_chunks': len(log_chunks),
            'model_used': self.config.model_type,
            'processing_time': datetime.now().isoformat()
        }
    
    def _generate_overall_summary(self, chunk_summaries: List[Dict[str, Any]]) -> str:
        """生成总体摘要"""
        if not chunk_summaries:
            return ""
        
        # 合并所有块摘要
        combined_summaries = "\n\n".join([
            f"Chunk {cs['chunk_id']} Summary:\n{cs['summary']}" 
            for cs in chunk_summaries
        ])
        
        overall_prompt = f"""
Based on the following individual chunk summaries, provide a comprehensive overall summary 
of the security threats and attack patterns identified across all log entries:

{combined_summaries}

Please provide a consolidated analysis covering:
1. Overall threat landscape
2. Most critical attack types
3. Key patterns and trends
4. Priority recommendations

Overall Summary:
"""
        
        return self.llm.generate_response(overall_prompt)
    
    def save_results(self, results: Dict[str, Any], filename: str = None) -> str:
        """保存结果到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"summarization_results_{timestamp}.json"
        
        output_path = self.output_dir / filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Results saved to: {output_path}")
        return str(output_path)
    
    def process_dataset(self, dataset_path: str, output_filename: str = None) -> str:
        """处理完整数据集"""
        logger.info("Starting dataset processing...")
        
        # 加载数据集
        raw_data = self.load_dataset(dataset_path)
        
        # 预处理日志
        processed_logs = self.preprocess_logs(raw_data)
        
        # 生成摘要
        results = self.summarize_logs(processed_logs)
        
        # 保存结果
        output_path = self.save_results(results, output_filename)
        
        logger.info("Dataset processing completed successfully!")
        return output_path

    def run_threat_classification_demo(self, data_file: str = "output_0515_dataset_fin.json") -> Dict[str, Any]:
        """运行威胁分类演示 - 任务1主要功能"""
        logger.info("开始运行威胁分类演示...")
        
        try:
            # 1. 加载未分类的威胁数据
            unclassified_data = self.load_unclassified_threat_data(data_file)
            
            # 2. 执行威胁分类和摘要
            classification_results = self.classify_and_summarize_threats(unclassified_data)
            
            # 3. 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"threat_classification_results_{timestamp}.json"
            output_path = self.save_results(classification_results, output_filename)
            
            # 4. 打印摘要信息
            self._print_classification_summary(classification_results)
            
            logger.info(f"威胁分类演示完成，结果已保存到: {output_path}")
            return classification_results
            
        except Exception as e:
            logger.error(f"威胁分类演示过程中发生错误: {e}")
            raise
    
    def _print_classification_summary(self, results: Dict[str, Any]):
        """打印分类结果摘要"""
        print("\n" + "="*60)
        print("威胁分类结果摘要")
        print("="*60)
        
        print(f"总威胁数量: {results['total_threats']}")
        print(f"识别的威胁类型数: {results['unique_threat_types']}")
        print(f"分类准确率: {results['classification_accuracy']:.1%}")
        print(f"处理时间: {results['processing_time']}")
        
        print("\n威胁类型分布:")
        print("-" * 40)
        for threat_type, stats in results['classification_stats'].items():
            print(f"{threat_type:15} | {stats['count']:3}次 ({stats['percentage']:5.1f}%) | {stats['severity']:8} | 置信度: {stats['average_confidence']:.3f}")
        
        print("\n总体摘要:")
        print("-" * 40)
        print(results['overall_summary'])
        
        # 显示前5个分类示例
        print("\n分类示例 (前5个):")
        print("-" * 40)
        for i, threat in enumerate(results['threat_summaries'][:5]):
            print(f"{i+1}. {threat['id']}: {threat['classified_type']} (置信度: {threat['confidence']:.3f})")
            print(f"   摘要: {threat['summary']}")
            if 'classification_correct' in threat:
                status = "✓" if threat['classification_correct'] else "✗"
                print(f"   验证: {status} (原始类型: {threat.get('original_type', 'N/A')})")
            print()

# 示例用法
def main():
    """主函数示例 - 威胁分类演示"""
    
    # 配置参数 - 使用rule_based模式进行快速测试
    config = SummarizationConfig(
        model_type="rule_based",  # 使用基于规则的分类器，无需加载LLM模型
        max_length=2048,
        temperature=0.7,
        batch_size=2,
        output_dir="./summarization_outputs",
        quantization=False
    )
    
    # 初始化Agent
    agent = SummarizationAgent(config)
    
    # 运行威胁分类演示
    try:
        results = agent.run_threat_classification_demo("output_0515_dataset_fin.json")
        print(f"\n演示完成！共处理 {results['total_threats']} 个威胁，分类准确率: {results['classification_accuracy']:.1%}")
    except Exception as e:
        print(f"演示运行失败: {e}")

def demo_with_llm():
    """使用LLM模型的演示（需要本地模型）"""
    
    # 配置参数 - 使用llama3模型
    config = SummarizationConfig(
        model_type="llama3-8b",
        model_path="meta-llama/Llama-3.1-8B-Instruct",  # 需要指定实际的模型路径
        max_length=2048,
        temperature=0.7,
        batch_size=1,
        output_dir="./summarization_outputs",
        quantization=True
    )
    
    # 初始化Agent
    agent = SummarizationAgent(config)
    
    # 运行威胁分类演示
    try:
        results = agent.run_threat_classification_demo("output_0515_dataset_fin.json")
        print(f"\n演示完成！共处理 {results['total_threats']} 个威胁，分类准确率: {results['classification_accuracy']:.1%}")
    except Exception as e:
        print(f"演示运行失败: {e}")

if __name__ == "__main__":
    # 默认运行基于规则的快速演示
    main()
    
    # 如果需要使用LLM模型，取消下面的注释
    # demo_with_llm()